<?php
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), array('.', '..'));

    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            if (is_writable($path)) {
                unlink($path);
                echo "Deleted file: $path<br>";
            } else {
                echo "Cannot delete file (no permission): $path<br>";
                // Try to change permissions first
                chmod($path, 0666);
                if (unlink($path)) {
                    echo "Deleted file after chmod: $path<br>";
                } else {
                    echo "Still cannot delete: $path<br>";
                }
            }
        }
    }

    if (is_writable($dir)) {
        return rmdir($dir);
    } else {
        chmod($dir, 0777);
        return rmdir($dir);
    }
}

$dokumentyDir = '/home/<USER>/www/temp/dokumenty';

echo "<h2>Cleaning up dokumenty directory</h2>";

if (is_dir($dokumentyDir)) {
    $folders = array_diff(scandir($dokumentyDir), array('.', '..'));

    foreach ($folders as $folder) {
        $folderPath = $dokumentyDir . '/' . $folder;
        if (is_dir($folderPath)) {
            echo "<h3>Processing folder: $folder</h3>";
            if (deleteDirectory($folderPath)) {
                echo "<p style='color: green;'>Successfully deleted: $folder</p>";
            } else {
                echo "<p style='color: red;'>Failed to delete: $folder</p>";
            }
        }
    }

    echo "<p><strong>Cleanup completed.</strong></p>";
} else {
    echo "<p style='color: red;'>Directory does not exist: $dokumentyDir</p>";
}
?>
